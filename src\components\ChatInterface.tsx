'use client';

import { useEffect, useRef } from 'react';
import { useChat } from '@/hooks/useChat';
import { MessageBubble } from './MessageBubble';
import { MessageInput } from './MessageInput';
import { TypingIndicator } from './TypingIndicator';
import { Sidebar } from './Sidebar';
import { MessageSquare } from 'lucide-react';

export function ChatInterface() {
  const {
    conversations,
    currentConversation,
    currentConversationId,
    isLoading,
    error,
    createNewConversation,
    selectConversation,
    deleteConversation,
    sendMessage,
  } = useChat();

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentConversation?.messages, isLoading]);

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto p-8">
        <MessageSquare size={64} className="mx-auto mb-6 text-gray-400" />
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Welcome to ChatGPT Clone
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Start a conversation by typing a message below. I'm here to help with questions, 
          creative tasks, analysis, and more.
        </p>
        <div className="grid grid-cols-1 gap-3 text-sm">
          <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <strong>💡 Examples:</strong>
            <ul className="mt-2 space-y-1 text-gray-600 dark:text-gray-400">
              <li>• "Explain quantum computing"</li>
              <li>• "Write a creative story"</li>
              <li>• "Help me plan a trip"</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-white dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar
        conversations={conversations}
        currentConversationId={currentConversationId}
        onSelectConversation={selectConversation}
        onNewConversation={createNewConversation}
        onDeleteConversation={deleteConversation}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col lg:ml-0 ml-0">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto">
          {!currentConversation || currentConversation.messages.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="max-w-4xl mx-auto">
              {currentConversation.messages.map((message) => (
                <MessageBubble key={message.id} message={message} />
              ))}
              
              {isLoading && <TypingIndicator />}
              
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="mx-auto max-w-4xl px-4 py-2">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-red-700 dark:text-red-400 text-sm">
                Error: {error}
              </p>
            </div>
          </div>
        )}

        {/* Message Input */}
        <MessageInput
          onSendMessage={sendMessage}
          disabled={isLoading}
          placeholder={isLoading ? "AI is responding..." : "Type your message..."}
        />
      </div>
    </div>
  );
}
