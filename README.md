# ChatGPT Clone

A modern ChatGPT clone built with Next.js 15, TypeScript, and Tailwind CSS. This application provides a clean, responsive chat interface with conversation management and local storage persistence.

## Features

- 🎨 **Modern UI**: Clean, responsive design inspired by ChatGPT
- 💬 **Real-time Chat**: Interactive chat interface with typing indicators
- 📱 **Mobile Responsive**: Works seamlessly on desktop and mobile devices
- 💾 **Local Storage**: Conversations are saved locally in your browser
- 🌙 **Dark Mode**: Automatic dark/light mode based on system preferences
- 📝 **Multiple Conversations**: Create and manage multiple chat sessions
- ⚡ **Fast Performance**: Built with Next.js 15 and optimized for speed
- 🔧 **TypeScript**: Fully typed for better development experience

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Icons**: Lucide React
- **State Management**: React Hooks + Local Storage
- **API**: Next.js API Routes

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chatgpt-clone
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/
│   ├── api/chat/          # Chat API endpoint
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/
│   ├── ChatInterface.tsx  # Main chat component
│   ├── MessageBubble.tsx  # Individual message display
│   ├── MessageInput.tsx   # Message input field
│   ├── Sidebar.tsx        # Conversation sidebar
│   └── TypingIndicator.tsx # Loading animation
├── hooks/
│   ├── useChat.ts         # Chat state management
│   └── useLocalStorage.ts # Local storage hook
└── types/
    └── chat.ts            # TypeScript interfaces
```

## Usage

1. **Start a New Chat**: Click the "New Chat" button in the sidebar
2. **Send Messages**: Type your message and press Enter or click the send button
3. **Switch Conversations**: Click on any conversation in the sidebar to switch
4. **Delete Conversations**: Hover over a conversation and click the trash icon
5. **Mobile Navigation**: Use the hamburger menu on mobile devices

## API Integration

The current implementation uses a mock API for demonstration. To integrate with a real AI service:

1. Update `src/app/api/chat/route.ts` to call your preferred AI API (OpenAI, Anthropic, etc.)
2. Add your API keys to environment variables
3. Modify the request/response handling as needed

Example for OpenAI integration:
```typescript
// Add to your .env.local
OPENAI_API_KEY=your_api_key_here

// Update the API route to use OpenAI
const response = await openai.chat.completions.create({
  model: "gpt-3.5-turbo",
  messages: [...messages, { role: "user", content: newMessage }],
});
```

## Customization

### Styling
- Modify `src/app/globals.css` for global styles
- Update Tailwind classes in components for UI changes
- Customize the color scheme in the CSS variables

### Features
- Add message editing functionality
- Implement message search
- Add file upload capabilities
- Integrate with different AI models

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Acknowledgments

- Inspired by OpenAI's ChatGPT interface
- Built with the amazing Next.js framework
- Icons provided by Lucide React
