import { Message } from '@/types/chat';
import { User, Bot } from 'lucide-react';
import clsx from 'clsx';

interface MessageBubbleProps {
  message: Message;
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user';

  return (
    <div className={clsx(
      'flex gap-3 p-4 group',
      isUser ? 'bg-transparent' : 'bg-gray-50 dark:bg-gray-800/50'
    )}>
      <div className={clsx(
        'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center',
        isUser 
          ? 'bg-blue-500 text-white' 
          : 'bg-green-500 text-white'
      )}>
        {isUser ? <User size={16} /> : <Bot size={16} />}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
          {isUser ? 'You' : 'Assistant'}
        </div>
        
        <div className="prose prose-sm max-w-none text-gray-700 dark:text-gray-300">
          <p className="whitespace-pre-wrap break-words">{message.content}</p>
        </div>
        
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </div>
    </div>
  );
}
