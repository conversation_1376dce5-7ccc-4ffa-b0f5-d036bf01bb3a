import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { messages, newMessage } = await request.json();

    // For demo purposes, we'll create a simple mock response
    // In a real application, you would integrate with OpenAI API or another AI service
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Mock responses based on message content
    const mockResponses = [
      "I understand your question. Let me help you with that.",
      "That's an interesting point. Here's what I think about it...",
      "I'd be happy to assist you with this topic.",
      "Based on what you've shared, I can provide some insights.",
      "Let me break this down for you step by step.",
      "That's a great question! Here's my perspective on it.",
      "I can help you explore this topic further.",
      "Thank you for sharing that. Here's what I would suggest...",
    ];

    // Simple logic to generate contextual responses
    let response = "";
    const lowerMessage = newMessage.toLowerCase();
    
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      response = "Hello! I'm here to help you with any questions or tasks you have. What would you like to discuss today?";
    } else if (lowerMessage.includes('help')) {
      response = "I'd be happy to help! I can assist with a wide variety of tasks including answering questions, writing, analysis, creative projects, and problem-solving. What specifically would you like help with?";
    } else if (lowerMessage.includes('what') && lowerMessage.includes('you')) {
      response = "I'm an AI assistant designed to help with various tasks. I can answer questions, help with writing, provide explanations, assist with analysis, and engage in creative projects. How can I assist you today?";
    } else if (lowerMessage.includes('thank')) {
      response = "You're very welcome! I'm glad I could help. Is there anything else you'd like to discuss or any other questions I can answer for you?";
    } else {
      // Use a random response for other messages
      response = mockResponses[Math.floor(Math.random() * mockResponses.length)];
      
      // Add some context based on message length
      if (newMessage.length > 100) {
        response += " I can see you've provided quite a bit of detail, which helps me understand your needs better.";
      }
      
      // Add a follow-up question occasionally
      if (Math.random() > 0.7) {
        response += " Would you like me to elaborate on any particular aspect?";
      }
    }

    return NextResponse.json({ 
      message: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
